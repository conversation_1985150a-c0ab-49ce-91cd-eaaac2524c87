"""
Pydantic models for the complaint classification system.

This module defines the data structures used throughout the system
for type safety and validation.
"""

from enum import Enum
from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, Field


class ActionType(str, Enum):
    """Action types for agent decisions."""

    FINALIZE = "finalize"
    ESCALATE = "escalate"


class DecisionType(str, Enum):
    """Review decision types."""

    PASS = "pass"
    FAIL = "fail"


class TriageResult(BaseModel):
    """Result from the Triage Agent."""

    action: ActionType
    category: Optional[str] = None
    confidence: float = Field(ge=0.0, le=1.0)
    reasoning: str


class CategoryCandidate(BaseModel):
    """A category candidate with confidence and reasoning."""

    category: str
    confidence: float = Field(ge=0.0, le=1.0)
    reasoning: str
    keywords_found: Optional[List[str]] = None
    evidence_from_text: Optional[str] = None


class MainCategoryResult(BaseModel):
    """Result from Main Category Analyzer."""

    candidates: List[CategoryCandidate] = Field(min_length=1, max_length=3)


class SubCategoryResult(BaseModel):
    """Result from Sub Category Analyzer."""

    parent_category: str
    candidates: List[CategoryCandidate] = Field(min_length=1, max_length=3)


class ReviewResult(BaseModel):
    """Result from Review Agent."""

    decision: DecisionType
    justification: str
    recommendation: Optional[str] = None


class ClassificationResult(BaseModel):
    """Final classification result."""

    category: str
    confidence: Optional[float] = None
    path_taken: Literal["fast_track", "expert_review"] = "expert_review"
    reasoning_chain: List[Dict[str, Any]] = Field(default_factory=list)
    review_passed: Optional[bool] = None


class ComplaintCase(BaseModel):
    """Input complaint case."""

    content: str
    case_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
