"""
Configuration settings for the complaint classification system.
"""

import os
from typing import Optional

from pydantic import BaseModel, <PERSON>
from pydantic_settings import BaseSettings


class ModelConfig(BaseModel):
    """Configuration for AI models."""

    provider: str = "openai"  # openai, anthropic, etc.
    model_id: str = "gpt-4o"
    temperature: float = Field(default=0.1, ge=0.0, le=2.0)
    max_tokens: Optional[int] = None
    api_key: Optional[str] = None


class AgentConfig(BaseModel):
    """Configuration for individual agents."""

    triage_model: ModelConfig = Field(
        default_factory=lambda: ModelConfig(
            provider="openai",
            model_id="gpt-4o-mini",  # Faster model for triage
            temperature=0.1,
        )
    )

    main_analyzer_model: ModelConfig = Field(
        default_factory=lambda: ModelConfig(
            provider="openai", model_id="gpt-4o", temperature=0.1
        )
    )

    sub_analyzer_model: ModelConfig = Field(
        default_factory=lambda: ModelConfig(
            provider="openai", model_id="gpt-4o", temperature=0.1
        )
    )

    review_model: ModelConfig = Field(
        default_factory=lambda: ModelConfig(
            provider="openai", model_id="gpt-4o", temperature=0.1
        )
    )

    orchestrator_model: ModelConfig = Field(
        default_factory=lambda: ModelConfig(
            provider="openai", model_id="gpt-4o", temperature=0.1
        )
    )


class WorkflowConfig(BaseModel):
    """Configuration for workflow behavior."""

    triage_confidence_threshold: float = Field(default=0.85, ge=0.0, le=1.0)
    parallel_analysis_threshold: float = Field(default=0.15, ge=0.0, le=1.0)
    max_candidates: int = Field(default=3, ge=1, le=5)
    enable_fast_track: bool = True
    fallback_category: str = "其他建議、諮詢或陳情"


class Settings(BaseSettings):
    """Application settings."""

    # Environment
    environment: str = Field(default="development")
    debug: bool = Field(default=False)

    # Knowledge base
    knowledge_base_path: str = Field(default="data/categories_def.json")

    # Agent configurations
    agents: AgentConfig = Field(default_factory=AgentConfig)

    # Workflow configurations
    workflow: WorkflowConfig = Field(default_factory=WorkflowConfig)

    # API Keys (loaded from environment)
    openai_api_key: Optional[str] = Field(
        default=os.getenv("OPENAI_API_KEY"), alias="OPENAI_API_KEY"
    )
    anthropic_api_key: Optional[str] = Field(default=None, alias="ANTHROPIC_API_KEY")

    # Logging
    log_level: str = Field(default="INFO")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        # Allow nested environment variables
        env_nested_delimiter = "__"


# Global settings instance
settings = Settings()
