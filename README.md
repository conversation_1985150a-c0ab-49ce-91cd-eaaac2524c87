# AI Multi-Agent Complaint Classification System

一個基於 Agno 框架的智能多代理陳情案件分類系統，採用「專家委員會」架構，通過多個專業化 AI 代理協作實現高準確度和可靠性的陳情案件自動分類。

## 🎯 系統特色

-   **多代理協作**: 6 個專業化代理分工合作
-   **雙軌工作流**: 快速通道 + 專家審議通道
-   **智能上下文注入**: 動態載入相關知識庫內容，優化 Token 使用
-   **完整推理鏈**: 記錄所有決策過程，提供透明度
-   **錯誤處理**: 完善的重試機制和降級處理
-   **高度可配置**: 支援多種 AI 模型和自定義參數

## 🏗️ 系統架構

### 代理組成

-   **分流代理 (TriageAgent)**: 快速初步評估和路由
-   **主分類分析器 (MainCategoryAnalyzer)**: 高層語義理解
-   **子分類分析器 (SubCategoryAnalyzer)**: 細粒度分類
-   **審查代理 (ReviewAgent)**: 品質保證和驗證
-   **輸出代理 (OutputAgent)**: 最終結果格式化
-   **協調代理 (OrchestratorAgent)**: 工作流協調

### 工作流程

1. **快速通道**: 簡單案件直接由分流代理處理
2. **專家審議**: 複雜案件通過完整專家委員會分析

## 🚀 快速開始

### 1. 環境設置

```bash
# 克隆專案
git clone <repository-url>
cd ai-application

# 安裝依賴
uv sync

```

### 2. 配置 API 金鑰

```bash
# 設置環境變數
export OPENAI_API_KEY="your-openai-api-key"
# 或
export ANTHROPIC_API_KEY="your-anthropic-api-key"
```

### 3. 驗證系統

```bash
# 運行系統測試
python test_system.py

# 運行單元測試
python -m pytest tests/ -v
```

### 4. 基本使用

```python
import asyncio
from complaint_classifier import ComplaintClassifier

async def main():
    # 初始化分類器
    classifier = ComplaintClassifier()

    # 分類陳情案件
    result = await classifier.classify(
        content="路燈不亮了，請派人修理",
        case_id="CASE001"
    )

    print(f"分類結果: {result.category}")
    print(f"信心度: {result.confidence}")
    print(f"處理路徑: {result.path_taken}")

asyncio.run(main())
```

## 📋 CLI 工具

系統提供命令列工具方便測試和使用：

```bash
# 分類單一陳情
python -m complaint_classifier.cli classify "路燈故障需要維修"

# 查看可用分類
python -m complaint_classifier.cli categories

# 驗證系統設置
python -m complaint_classifier.cli validate
```

## 🔧 配置選項

### 模型配置

```python
from complaint_classifier.config import Settings, ModelConfig

settings = Settings()

# 自定義模型配置
settings.agents.triage_model.model_id = "gpt-4o-mini"
settings.agents.main_analyzer_model.model_id = "gpt-4o"
settings.agents.main_analyzer_model.temperature = 0.1
```

### 工作流配置

```python
# 調整工作流參數
settings.workflow.triage_confidence_threshold = 0.85
settings.workflow.enable_fast_track = True
settings.workflow.fallback_category = "其他建議、諮詢或陳情"
```

## 📊 知識庫管理

系統使用 JSON 格式的知識庫定義分類結構：

```json
{
	"道路、水溝維護": {
		"description": "道路及水溝相關維護問題",
		"sub_categories": {
			"路面破損": {
				"description": "路面坑洞、破損等問題"
			},
			"水溝阻塞": {
				"description": "水溝、排水系統阻塞問題"
			}
		}
	}
}
```

## 🧪 測試

### 運行所有測試

```bash
python -m pytest tests/ -v
```

### 運行特定測試

```bash
# 測試知識庫
python -m pytest tests/test_knowledge.py -v

# 測試分類器
python -m pytest tests/test_classifier.py -v
```

## 📈 性能監控

### 獲取系統統計

```python
classifier = ComplaintClassifier()
stats = classifier.get_statistics()

print(f"主分類數量: {stats['knowledge_base']['main_categories']}")
print(f"子分類數量: {stats['knowledge_base']['total_sub_categories']}")
```

## 🔍 故障排除

### 常見問題

1. **API 金鑰錯誤**

    ```bash
    export OPENAI_API_KEY="your-correct-api-key"
    ```

2. **知識庫載入失敗**

    - 檢查 `data/categories_def.json` 檔案是否存在
    - 驗證 JSON 格式是否正確

3. **模組導入錯誤**
    - 確保在正確的虛擬環境中
    - 檢查 PYTHONPATH 設置

## 📚 API 參考

### ComplaintClassifier

主要分類器介面：

-   `classify(content, case_id=None, metadata=None)`: 分類陳情案件
-   `get_available_categories()`: 獲取可用分類
-   `get_statistics()`: 獲取系統統計
-   `validate_setup()`: 驗證系統設置

### ClassificationResult

分類結果模型：

-   `category`: 分類結果
-   `confidence`: 信心度 (0-1)
-   `path_taken`: 處理路徑 ("fast_track" 或 "expert_review")
-   `reasoning_chain`: 推理鏈
-   `review_passed`: 審查結果
