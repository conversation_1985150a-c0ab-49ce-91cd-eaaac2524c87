"""
Triage Agent for initial complaint classification and routing.
"""

import json
import logging
from typing import Any

from agno.agent import Agent

from .base import BaseClassificationAgent
from ..models import TriageResult, ComplaintCase
from ..config import ModelConfig

logger = logging.getLogger(__name__)


class TriageAgent(BaseClassificationAgent):
    """
    Triage Agent - The system's "front desk receptionist".
    
    Responsibilities:
    - Fast initial assessment of complaint clarity
    - Route simple cases to fast track (direct classification)
    - Route complex cases to expert review path
    - Minimize cost and maximize speed for initial filtering
    """
    
    def __init__(self, model_config: ModelConfig):
        """Initialize the Triage Agent.
        
        Args:
            model_config: Configuration for the AI model
        """
        super().__init__(
            model_config=model_config,
            name="Triage Agent",
            role="Initial complaint assessment and routing specialist"
        )
    
    def _get_system_instructions(self) -> list[str]:
        """Get system instructions for the Triage Agent."""
        return [
            "你是市民陳情案件的預檢分流專員，負責快速判斷案件的明確度並進行分流。",
            "你的主要任務是以最高速度和最低成本判斷案件是否可以直接分類。",
            "對於明確、簡單的案件，你應該直接給出分類結果（action: 'finalize'）。",
            "對於複雜、模糊或涉及多個領域的案件，你應該將其轉交給專家委員會（action: 'escalate'）。",
            "你必須以 JSON 格式回應，包含 action、category、confidence 和 reasoning 欄位。",
            "confidence 分數應該反映你對分類決策的信心程度（0.0-1.0）。",
            "reasoning 應該簡潔說明你的判斷依據。"
        ]
    
    def _create_agent(self) -> Agent:
        """Create the Triage Agent."""
        additional_instructions = [
            "回應格式範例：",
            "高明確度案件：{\"action\": \"finalize\", \"category\": \"路燈故障\", \"confidence\": 0.95, \"reasoning\": \"案件明確描述路燈不亮，直接對應路燈故障類別。\"}",
            "低明確度案件：{\"action\": \"escalate\", \"category\": null, \"confidence\": 0.60, \"reasoning\": \"案件同時涉及噪音和交通問題，需要專家進一步分析。\"}"
        ]
        
        return self._create_base_agent(additional_instructions)
    
    async def _process_internal(self, complaint: ComplaintCase, **kwargs) -> TriageResult:
        """Process complaint for initial triage.
        
        Args:
            complaint: The complaint case to process
            **kwargs: Additional arguments
            
        Returns:
            Triage result with routing decision
        """
        prompt = self._create_triage_prompt(complaint.content)
        
        # Get response from agent
        response = await self.agent.arun(prompt)
        
        # Parse JSON response
        try:
            result_data = json.loads(response.content)
            return TriageResult(**result_data)
        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"Failed to parse triage response: {e}")
            # Fallback to escalation if parsing fails
            return TriageResult(
                action="escalate",
                category=None,
                confidence=0.0,
                reasoning=f"解析回應失敗，轉交專家處理: {str(e)}"
            )
    
    def _create_triage_prompt(self, complaint_content: str) -> str:
        """Create the prompt for triage analysis.
        
        Args:
            complaint_content: The complaint text content
            
        Returns:
            Formatted prompt string
        """
        return f"""請分析以下市民陳情案件，判斷是否可以直接分類或需要轉交專家委員會：

**陳情內容：**
{complaint_content}

**分析要求：**
1. 如果案件內容明確、單一主題，且你有高信心（>0.85）可以直接分類，請選擇 action: "finalize"
2. 如果案件內容複雜、模糊、涉及多個領域，或你的信心度較低，請選擇 action: "escalate"
3. 對於 finalize 案件，請提供具體的子案類名稱
4. 對於 escalate 案件，category 設為 null

請以 JSON 格式回應，包含 action、category、confidence、reasoning 四個欄位。"""
