"""
Tests for the knowledge management system.
"""

import pytest
import json
import tempfile
from pathlib import Path

from src.complaint_classifier.knowledge.manager import KnowledgeManager
from src.complaint_classifier.knowledge.context_injector import ContextInjector
from src.complaint_classifier.knowledge.models import KnowledgeBase, CategoryDefinition, SubCategoryDefinition


class TestKnowledgeManager:
    """Test cases for KnowledgeManager."""
    
    @pytest.fixture
    def sample_knowledge_data(self):
        """Create sample knowledge base data."""
        return {
            "道路、水溝維護": {
                "description": "道路及水溝相關維護問題",
                "sub_categories": {
                    "路面破損": {
                        "description": "路面坑洞、破損等問題"
                    },
                    "水溝阻塞": {
                        "description": "水溝、排水系統阻塞問題"
                    }
                }
            },
            "路燈維護": {
                "description": "路燈相關維護問題",
                "sub_categories": {
                    "路燈故障": {
                        "description": "路燈不亮、閃爍等故障"
                    }
                }
            }
        }
    
    @pytest.fixture
    def temp_knowledge_file(self, sample_knowledge_data):
        """Create a temporary knowledge base file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(sample_knowledge_data, f, ensure_ascii=False, indent=2)
            temp_path = f.name
        
        yield temp_path
        
        # Cleanup
        Path(temp_path).unlink()
    
    @pytest.fixture
    def knowledge_manager(self, temp_knowledge_file):
        """Create a KnowledgeManager instance."""
        return KnowledgeManager(temp_knowledge_file)
    
    def test_load_knowledge_base(self, knowledge_manager):
        """Test loading knowledge base from file."""
        kb = knowledge_manager.load_knowledge_base()
        
        assert isinstance(kb, KnowledgeBase)
        assert len(kb.categories) == 2
        assert "道路、水溝維護" in kb.categories
        assert "路燈維護" in kb.categories
    
    def test_get_main_category_definitions(self, knowledge_manager):
        """Test getting main category definitions."""
        definitions = knowledge_manager.get_main_category_definitions()
        
        assert isinstance(definitions, dict)
        assert len(definitions) == 2
        assert "道路、水溝維護" in definitions
        assert definitions["道路、水溝維護"] == "道路及水溝相關維護問題"
    
    def test_get_sub_category_definitions(self, knowledge_manager):
        """Test getting sub-category definitions."""
        sub_defs = knowledge_manager.get_sub_category_definitions("道路、水溝維護")
        
        assert isinstance(sub_defs, dict)
        assert len(sub_defs) == 2
        assert "路面破損" in sub_defs
        assert "水溝阻塞" in sub_defs
        assert sub_defs["路面破損"] == "路面坑洞、破損等問題"
    
    def test_get_sub_category_definitions_invalid_main(self, knowledge_manager):
        """Test getting sub-categories for invalid main category."""
        sub_defs = knowledge_manager.get_sub_category_definitions("不存在的分類")
        
        assert isinstance(sub_defs, dict)
        assert len(sub_defs) == 0
    
    def test_validate_classification(self, knowledge_manager):
        """Test classification validation."""
        # Valid classification
        assert knowledge_manager.validate_classification("道路、水溝維護", "路面破損") is True
        
        # Invalid main category
        assert knowledge_manager.validate_classification("不存在的主分類", "路面破損") is False
        
        # Invalid sub-category
        assert knowledge_manager.validate_classification("道路、水溝維護", "不存在的子分類") is False
    
    def test_get_statistics(self, knowledge_manager):
        """Test getting knowledge base statistics."""
        stats = knowledge_manager.get_statistics()
        
        assert isinstance(stats, dict)
        assert "main_categories" in stats
        assert "total_sub_categories" in stats
        assert "avg_sub_categories_per_main" in stats
        
        assert stats["main_categories"] == 2
        assert stats["total_sub_categories"] == 3
        assert stats["avg_sub_categories_per_main"] == 1.5
    
    def test_load_invalid_json(self):
        """Test loading invalid JSON file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write("invalid json content")
            temp_path = f.name
        
        try:
            manager = KnowledgeManager(temp_path)
            with pytest.raises(ValueError, match="Invalid JSON"):
                manager.load_knowledge_base()
        finally:
            Path(temp_path).unlink()
    
    def test_load_nonexistent_file(self):
        """Test loading non-existent file."""
        manager = KnowledgeManager("nonexistent.json")
        with pytest.raises(FileNotFoundError):
            manager.load_knowledge_base()


class TestContextInjector:
    """Test cases for ContextInjector."""
    
    @pytest.fixture
    def knowledge_manager(self):
        """Create a mock knowledge manager."""
        # Use the real knowledge base file for testing
        return KnowledgeManager("src/data/categories_def.json")
    
    @pytest.fixture
    def context_injector(self, knowledge_manager):
        """Create a ContextInjector instance."""
        return ContextInjector(knowledge_manager)
    
    def test_create_main_category_context(self, context_injector):
        """Test creating main category context."""
        context = context_injector.create_main_category_context()
        
        assert context.main_categories is not None
        assert isinstance(context.main_categories, dict)
        assert len(context.main_categories) > 0
        assert context.sub_categories is None
        assert context.selected_main_category is None
    
    def test_create_sub_category_context(self, context_injector):
        """Test creating sub-category context."""
        # First get a valid main category
        main_cats = context_injector.knowledge_manager.get_main_category_definitions()
        main_cat = list(main_cats.keys())[0]
        
        context = context_injector.create_sub_category_context(main_cat)
        
        assert context.sub_categories is not None
        assert isinstance(context.sub_categories, dict)
        assert context.selected_main_category == main_cat
        assert context.main_categories is None
    
    def test_create_review_context(self, context_injector):
        """Test creating review context."""
        # Get valid categories
        main_cats = context_injector.knowledge_manager.get_main_category_definitions()
        main_cat = list(main_cats.keys())[0]
        sub_cats = context_injector.knowledge_manager.get_sub_category_definitions(main_cat)
        sub_cat = list(sub_cats.keys())[0]
        
        context = context_injector.create_review_context(main_cat, sub_cat)
        
        assert context.main_categories is not None
        assert context.sub_categories is not None
        assert context.selected_main_category == main_cat
        assert context.selected_sub_category == sub_cat
    
    def test_format_main_category_prompt_section(self, context_injector):
        """Test formatting main category prompt section."""
        context = context_injector.create_main_category_context()
        formatted = context_injector.format_main_category_prompt_section(context)
        
        assert isinstance(formatted, str)
        assert "主案類清單與定義" in formatted
        assert len(formatted) > 0
    
    def test_format_sub_category_prompt_section(self, context_injector):
        """Test formatting sub-category prompt section."""
        # Get a valid main category
        main_cats = context_injector.knowledge_manager.get_main_category_definitions()
        main_cat = list(main_cats.keys())[0]
        
        context = context_injector.create_sub_category_context(main_cat)
        formatted = context_injector.format_sub_category_prompt_section(context)
        
        assert isinstance(formatted, str)
        assert "子案類清單與定義" in formatted
        assert main_cat in formatted
    
    def test_format_review_prompt_section(self, context_injector):
        """Test formatting review prompt section."""
        # Get valid categories
        main_cats = context_injector.knowledge_manager.get_main_category_definitions()
        main_cat = list(main_cats.keys())[0]
        sub_cats = context_injector.knowledge_manager.get_sub_category_definitions(main_cat)
        sub_cat = list(sub_cats.keys())[0]
        
        context = context_injector.create_review_context(main_cat, sub_cat)
        formatted = context_injector.format_review_prompt_section(context)
        
        assert isinstance(formatted, str)
        assert "官方定義查核" in formatted
        assert main_cat in formatted
        assert sub_cat in formatted


class TestKnowledgeModels:
    """Test cases for knowledge models."""
    
    def test_sub_category_definition(self):
        """Test SubCategoryDefinition model."""
        sub_cat = SubCategoryDefinition(description="測試描述")
        assert sub_cat.description == "測試描述"
    
    def test_category_definition(self):
        """Test CategoryDefinition model."""
        sub_cat = SubCategoryDefinition(description="子分類描述")
        cat = CategoryDefinition(
            description="主分類描述",
            sub_categories={"子分類": sub_cat}
        )
        
        assert cat.description == "主分類描述"
        assert len(cat.sub_categories) == 1
        assert "子分類" in cat.sub_categories
    
    def test_knowledge_base_methods(self):
        """Test KnowledgeBase model methods."""
        sub_cat = SubCategoryDefinition(description="子分類描述")
        cat = CategoryDefinition(
            description="主分類描述",
            sub_categories={"子分類": sub_cat}
        )
        kb = KnowledgeBase(categories={"主分類": cat})
        
        # Test get_main_categories
        main_cats = kb.get_main_categories()
        assert main_cats == ["主分類"]
        
        # Test get_sub_categories
        sub_cats = kb.get_sub_categories("主分類")
        assert sub_cats == ["子分類"]
        
        # Test get_main_category_description
        desc = kb.get_main_category_description("主分類")
        assert desc == "主分類描述"
        
        # Test get_sub_category_description
        sub_desc = kb.get_sub_category_description("主分類", "子分類")
        assert sub_desc == "子分類描述"
        
        # Test validate_category_path
        assert kb.validate_category_path("主分類", "子分類") is True
        assert kb.validate_category_path("主分類", "不存在") is False
        assert kb.validate_category_path("不存在", "子分類") is False
