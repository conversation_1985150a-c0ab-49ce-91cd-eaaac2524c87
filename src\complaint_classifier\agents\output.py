"""
Output Agent for final result formatting.
"""

import logging
from typing import Any, Dict

from .base import BaseClassificationAgent
from ..models import ClassificationResult
from ..config import ModelConfig

logger = logging.getLogger(__name__)


class OutputAgent(BaseClassificationAgent):
    """
    Output Agent - Final result formatting specialist.
    
    Responsibilities:
    - Format final classification results
    - Ensure output conforms to required format
    - Handle fallback scenarios
    - Provide consistent output structure
    """
    
    def __init__(self, model_config: ModelConfig):
        """Initialize the Output Agent.
        
        Args:
            model_config: Configuration for the AI model (though this agent may not use LLM)
        """
        super().__init__(
            model_config=model_config,
            name="Output Agent",
            role="Final result formatting and output specialist"
        )
    
    def _get_system_instructions(self) -> list[str]:
        """Get system instructions for the Output Agent."""
        return [
            "你是結果格式化專員，負責確保最終輸出符合系統要求的格式。",
            "你的任務是將分類結果格式化為標準的 JSON 格式。",
            "你必須確保輸出格式的一致性和正確性。"
        ]
    
    def _create_agent(self):
        """Create the Output Agent (may not need actual LLM for simple formatting)."""
        # For simple formatting, we might not need an actual LLM agent
        # This can be implemented as pure Python logic
        return None
    
    async def _process_internal(self, classification_data: Dict[str, Any], **kwargs) -> ClassificationResult:
        """Process and format the final classification result.
        
        Args:
            classification_data: Dictionary containing classification information
            **kwargs: Additional arguments
            
        Returns:
            Formatted classification result
        """
        # Extract data from the classification_data dictionary
        category = classification_data.get("category")
        confidence = classification_data.get("confidence")
        path_taken = classification_data.get("path_taken", "expert_review")
        reasoning_chain = classification_data.get("reasoning_chain", [])
        review_passed = classification_data.get("review_passed")
        
        # Validate required fields
        if not category:
            logger.error("No category provided in classification data")
            category = "其他建議、諮詢或陳情"  # Fallback category
            confidence = 0.0
        
        # Create the final result
        result = ClassificationResult(
            category=category,
            confidence=confidence,
            path_taken=path_taken,
            reasoning_chain=reasoning_chain,
            review_passed=review_passed
        )
        
        logger.info(f"Formatted final classification result: {category}")
        return result
    
    def format_simple_output(self, category: str) -> Dict[str, str]:
        """Format a simple output for external systems.
        
        Args:
            category: The final category classification
            
        Returns:
            Simple dictionary with category
        """
        return {"category": category}
    
    def format_detailed_output(self, result: ClassificationResult) -> Dict[str, Any]:
        """Format a detailed output with full information.
        
        Args:
            result: The classification result
            
        Returns:
            Detailed dictionary with all information
        """
        return {
            "category": result.category,
            "confidence": result.confidence,
            "path_taken": result.path_taken,
            "reasoning_chain": result.reasoning_chain,
            "review_passed": result.review_passed,
            "metadata": {
                "agent_system": "complaint_classifier",
                "version": "1.0.0"
            }
        }
