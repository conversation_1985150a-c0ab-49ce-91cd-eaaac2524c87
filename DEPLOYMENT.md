# 部署指南

本文檔說明如何部署 AI Multi-Agent Complaint Classification System。

## 🚀 部署選項

### 1. 本地開發部署

#### 前置需求
- Python 3.11+
- uv 或 pip
- Git

#### 步驟

```bash
# 1. 克隆專案
git clone <repository-url>
cd ai-application

# 2. 安裝依賴
uv sync
# 或使用 pip
pip install -e .

# 3. 配置環境變數
cp .env.example .env
# 編輯 .env 文件，設置 API 金鑰

# 4. 驗證安裝
python test_system.py

# 5. 運行測試
python -m pytest tests/ -v
```

### 2. Docker 部署

#### 前置需求
- Docker
- Docker Compose

#### 步驟

```bash
# 1. 克隆專案
git clone <repository-url>
cd ai-application

# 2. 配置環境變數
cp .env.example .env
# 編輯 .env 文件，設置 API 金鑰

# 3. 構建並啟動容器
docker-compose up -d

# 4. 檢查容器狀態
docker-compose ps

# 5. 查看日誌
docker-compose logs complaint-classifier

# 6. 測試系統
docker-compose exec complaint-classifier python test_system.py
```

### 3. 生產環境部署

#### 使用 Docker Swarm

```bash
# 1. 初始化 Swarm
docker swarm init

# 2. 部署服務
docker stack deploy -c docker-compose.yml complaint-classifier

# 3. 檢查服務狀態
docker service ls
docker service logs complaint-classifier_complaint-classifier
```

#### 使用 Kubernetes

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: complaint-classifier
spec:
  replicas: 3
  selector:
    matchLabels:
      app: complaint-classifier
  template:
    metadata:
      labels:
        app: complaint-classifier
    spec:
      containers:
      - name: complaint-classifier
        image: complaint-classifier:latest
        ports:
        - containerPort: 8000
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: openai-api-key
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: complaint-classifier-service
spec:
  selector:
    app: complaint-classifier
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
```

## 🔧 配置管理

### 環境變數

| 變數名 | 描述 | 預設值 | 必需 |
|--------|------|--------|------|
| `OPENAI_API_KEY` | OpenAI API 金鑰 | - | 是* |
| `ANTHROPIC_API_KEY` | Anthropic API 金鑰 | - | 是* |
| `ENVIRONMENT` | 運行環境 | development | 否 |
| `LOG_LEVEL` | 日誌級別 | INFO | 否 |
| `KNOWLEDGE_BASE_PATH` | 知識庫文件路徑 | data/categories_def.json | 否 |
| `ENABLE_FAST_TRACK` | 啟用快速通道 | true | 否 |

*至少需要一個 API 金鑰

### 模型配置

系統支援多種 AI 模型配置：

```bash
# OpenAI 模型
TRIAGE_MODEL_ID=gpt-4o-mini
MAIN_ANALYZER_MODEL_ID=gpt-4o
SUB_ANALYZER_MODEL_ID=gpt-4o
REVIEW_MODEL_ID=gpt-4o

# Anthropic 模型
TRIAGE_MODEL_PROVIDER=anthropic
TRIAGE_MODEL_ID=claude-3-haiku-20240307
MAIN_ANALYZER_MODEL_PROVIDER=anthropic
MAIN_ANALYZER_MODEL_ID=claude-3-sonnet-20240229
```

## 📊 監控與日誌

### 健康檢查

系統提供內建健康檢查端點：

```bash
# 檢查系統狀態
python -c "
import asyncio
from src.complaint_classifier import ComplaintClassifier
result = asyncio.run(ComplaintClassifier().validate_setup())
print('健康狀態:', 'OK' if all(result.values()) else 'ERROR')
"
```

### 日誌配置

```python
# 在 .env 中配置
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
```

### 性能監控

```python
# 獲取系統統計
from complaint_classifier import ComplaintClassifier

classifier = ComplaintClassifier()
stats = classifier.get_statistics()
print(f"知識庫: {stats['knowledge_base']['main_categories']} 主分類")
print(f"配置: {stats['configuration']}")
```

## 🔒 安全考量

### API 金鑰管理

1. **環境變數**: 使用環境變數存儲 API 金鑰
2. **密鑰管理**: 在生產環境使用 Kubernetes Secrets 或 Docker Secrets
3. **輪換**: 定期輪換 API 金鑰

### 網路安全

1. **HTTPS**: 在生產環境使用 HTTPS
2. **防火牆**: 限制入站連接
3. **VPN**: 使用 VPN 保護內部通信

## 🚨 故障排除

### 常見問題

1. **API 金鑰錯誤**
   ```bash
   # 檢查環境變數
   echo $OPENAI_API_KEY
   
   # 測試 API 連接
   python -c "
   import openai
   client = openai.OpenAI()
   print(client.models.list())
   "
   ```

2. **知識庫載入失敗**
   ```bash
   # 檢查文件存在
   ls -la data/categories_def.json
   
   # 驗證 JSON 格式
   python -c "
   import json
   with open('data/categories_def.json') as f:
       data = json.load(f)
   print('JSON 格式正確')
   "
   ```

3. **記憶體不足**
   ```bash
   # 增加 Docker 記憶體限制
   docker run --memory=2g complaint-classifier
   
   # 或在 docker-compose.yml 中設置
   deploy:
     resources:
       limits:
         memory: 2G
   ```

### 日誌分析

```bash
# Docker 日誌
docker-compose logs -f complaint-classifier

# 系統日誌
tail -f logs/complaint_classifier.log

# 錯誤日誌
grep ERROR logs/complaint_classifier.log
```

## 📈 擴展與優化

### 水平擴展

```yaml
# docker-compose.yml
services:
  complaint-classifier:
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
```

### 快取優化

```python
# 啟用 Redis 快取 (未來功能)
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600
```

### 負載平衡

```nginx
# nginx.conf
upstream complaint_classifier {
    server complaint-classifier-1:8000;
    server complaint-classifier-2:8000;
    server complaint-classifier-3:8000;
}

server {
    listen 80;
    location / {
        proxy_pass http://complaint_classifier;
    }
}
```

## 🔄 更新與維護

### 滾動更新

```bash
# Docker Compose
docker-compose pull
docker-compose up -d

# Kubernetes
kubectl set image deployment/complaint-classifier \
  complaint-classifier=complaint-classifier:new-version
```

### 備份

```bash
# 備份知識庫
cp data/categories_def.json backup/categories_def_$(date +%Y%m%d).json

# 備份配置
cp .env backup/env_$(date +%Y%m%d).backup
```
