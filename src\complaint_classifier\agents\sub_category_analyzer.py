"""
Sub Category Analyzer Agent for fine-grained classification.
"""

import json
import logging
from typing import Any

from agno.agent import Agent

from .base import BaseClassificationAgent
from ..models import SubCategoryResult, ComplaintCase
from ..config import ModelConfig
from ..knowledge.context_injector import ContextInjector

logger = logging.getLogger(__name__)


class SubCategoryAnalyzer(BaseClassificationAgent):
    """
    Sub Category Analyzer Agent - Fine-grained classification specialist.
    
    Responsibilities:
    - Perform precise sub-category matching within a confirmed main category
    - Focus on detailed analysis and exact matching
    - Provide 1-3 most likely sub-category candidates
    - Ensure sub-categories belong to the specified main category
    """
    
    def __init__(self, model_config: ModelConfig, context_injector: ContextInjector):
        """Initialize the Sub Category Analyzer.
        
        Args:
            model_config: Configuration for the AI model
            context_injector: Context injector for knowledge base access
        """
        super().__init__(
            model_config=model_config,
            name="Sub Category Analyzer",
            role="Fine-grained sub-category classification specialist"
        )
        self.context_injector = context_injector
    
    def _get_system_instructions(self) -> list[str]:
        """Get system instructions for the Sub Category Analyzer."""
        return [
            "你是一位專精於特定業務領域的承辦員，負責精細化分類。",
            "你的任務是在已確定的主案類範疇內，進行最精準的子項目匹配。",
            "你只能從指定主案類下的子案類清單中選擇，不可跨越到其他主案類。",
            "請從提供的子案類清單中選出最相關的 1-3 個候選，按信心度排序。",
            "每個候選都要包含 sub_category、confidence、reasoning 和 evidence_from_text。",
            "confidence 分數應該反映你對該分類的信心程度（0.0-1.0）。",
            "reasoning 應該詳細說明為什麼選擇這個子案類。",
            "evidence_from_text 應該引用案件中的具體文字作為證據。",
            "你必須以 JSON 格式回應，包含 parent_category 和 candidates 陣列。"
        ]
    
    def _create_agent(self) -> Agent:
        """Create the Sub Category Analyzer Agent."""
        additional_instructions = [
            "回應格式範例：",
            "{\"parent_category\": \"路霸排除\",",
            " \"candidates\": [",
            "   {\"sub_category\": \"占用道路、騎樓及人行道\", \"confidence\": 0.95, \"reasoning\": \"案文明確描述『店家將桌椅和雜物堆放在人行道上』，完全符合此子案類定義。\", \"evidence_from_text\": \"店家桌椅、雜物、堆放、人行道\"}",
            " ]}"
        ]
        
        return self._create_base_agent(additional_instructions)
    
    async def _process_internal(self, complaint: ComplaintCase, main_category: str, **kwargs) -> SubCategoryResult:
        """Process complaint for sub-category analysis.
        
        Args:
            complaint: The complaint case to process
            main_category: The confirmed main category
            **kwargs: Additional arguments
            
        Returns:
            Sub-category analysis result
        """
        # Get context for the specific main category's sub-categories
        context = self.context_injector.create_sub_category_context(main_category)
        
        # Create prompt with injected context
        prompt = self._create_analysis_prompt(complaint.content, main_category, context)
        
        # Get response from agent
        response = await self.agent.arun(prompt)
        
        # Parse JSON response
        try:
            result_data = json.loads(response.content)
            return SubCategoryResult(**result_data)
        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"Failed to parse sub-category analysis response: {e}")
            # Fallback to a default response
            fallback_sub_category = f"{main_category}_其他"
            return SubCategoryResult(
                parent_category=main_category,
                candidates=[{
                    "category": fallback_sub_category,
                    "confidence": 0.1,
                    "reasoning": f"解析回應失敗，使用預設子分類: {str(e)}",
                    "evidence_from_text": ""
                }]
            )
    
    def _create_analysis_prompt(self, complaint_content: str, main_category: str, context) -> str:
        """Create the prompt for sub-category analysis.
        
        Args:
            complaint_content: The complaint text content
            main_category: The confirmed main category
            context: Context data with sub-category definitions
            
        Returns:
            Formatted prompt string
        """
        # Format the sub-category definitions
        category_section = self.context_injector.format_sub_category_prompt_section(context)
        
        return f"""你是一位專精於「{main_category}」業務的承辦員。此案件已被初步判定屬於你的業務範疇。請根據陳情內容，從下方專屬於【{main_category}】的子案類清單中，選擇最精準的分類。

{category_section}

**市民陳情內容:**
{complaint_content}

**分析要求:**
1. 仔細分析陳情內容的具體問題和詳細描述
2. 從上述子案類中選擇最相關的 1-3 個候選
3. 按信心度由高到低排序
4. 為每個候選提供詳細的判斷理由
5. 引用案件中的具體文字作為分類證據
6. 確保所選的子案類確實屬於「{main_category}」主案類

請以 JSON 格式回傳你的分析結果，包含 parent_category 和 candidates 陣列。"""
