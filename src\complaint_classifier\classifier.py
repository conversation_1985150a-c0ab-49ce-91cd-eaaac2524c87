"""
Main complaint classifier interface.
"""

import logging
from typing import Dict, Any, Optional

from .models import ComplaintCase, ClassificationResult
from .workflow import WorkflowEngine
from .config import Settings

logger = logging.getLogger(__name__)


class ComplaintClassifier:
    """
    Main interface for the AI Multi-Agent Complaint Classification System.
    
    This class provides a simple interface for classifying citizen complaints
    using the expert committee approach with multiple specialized AI agents.
    """
    
    def __init__(self, settings: Optional[Settings] = None):
        """Initialize the complaint classifier.
        
        Args:
            settings: Application settings. If None, default settings will be used.
        """
        self.settings = settings or Settings()
        self.workflow_engine = WorkflowEngine(self.settings)
        
        logger.info("ComplaintClassifier initialized")
    
    async def classify(
        self, 
        content: str, 
        case_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ClassificationResult:
        """Classify a citizen complaint.
        
        Args:
            content: The complaint text content
            case_id: Optional case identifier
            metadata: Optional metadata dictionary
            
        Returns:
            Classification result with category and reasoning
        """
        # Create complaint case
        complaint = ComplaintCase(
            content=content,
            case_id=case_id,
            metadata=metadata
        )
        
        logger.info(f"Classifying complaint: {case_id or 'unnamed'}")
        
        # Process through workflow engine
        result = await self.workflow_engine.classify_complaint(complaint)
        
        logger.info(f"Classification complete: {result.category}")
        return result
    
    def get_available_categories(self) -> Dict[str, Dict[str, str]]:
        """Get all available categories from the knowledge base.
        
        Returns:
            Dictionary with main categories and their sub-categories
        """
        kb = self.workflow_engine.knowledge_manager.knowledge_base
        
        categories = {}
        for main_cat in kb.get_main_categories():
            categories[main_cat] = {
                "description": kb.get_main_category_description(main_cat),
                "sub_categories": self.workflow_engine.knowledge_manager.get_sub_category_definitions(main_cat)
            }
        
        return categories
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get system statistics.
        
        Returns:
            Dictionary with system statistics
        """
        kb_stats = self.workflow_engine.knowledge_manager.get_statistics()
        
        return {
            "knowledge_base": kb_stats,
            "configuration": {
                "fast_track_enabled": self.settings.workflow.enable_fast_track,
                "triage_threshold": self.settings.workflow.triage_confidence_threshold,
                "fallback_category": self.settings.workflow.fallback_category
            }
        }
    
    async def validate_setup(self) -> Dict[str, bool]:
        """Validate that the system is properly set up.
        
        Returns:
            Dictionary with validation results
        """
        results = {}
        
        try:
            # Test knowledge base loading
            kb = self.workflow_engine.knowledge_manager.knowledge_base
            results["knowledge_base"] = len(kb.categories) > 0
        except Exception as e:
            logger.error(f"Knowledge base validation failed: {e}")
            results["knowledge_base"] = False
        
        try:
            # Test agent initialization
            agents = [
                self.workflow_engine.triage_agent,
                self.workflow_engine.main_analyzer,
                self.workflow_engine.sub_analyzer,
                self.workflow_engine.review_agent,
                self.workflow_engine.output_agent
            ]
            results["agents"] = all(agent is not None for agent in agents)
        except Exception as e:
            logger.error(f"Agent validation failed: {e}")
            results["agents"] = False
        
        try:
            # Test configuration
            results["configuration"] = (
                self.settings.openai_api_key is not None or 
                self.settings.anthropic_api_key is not None
            )
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            results["configuration"] = False
        
        return results
