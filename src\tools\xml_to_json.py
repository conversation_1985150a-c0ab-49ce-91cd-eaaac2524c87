from __future__ import annotations

import argparse
import json
import sys
from typing import Any, Dict, List, Tuple
from xml.etree import ElementTree as ET

# ===== 1) 你的知識對照表（保持不變，略） =====
category_knowledge = {
    "交通號誌、標誌、標線及大眾運輸": [
        "公車問題及站牌、候車亭設施管理",
        "交通標誌、標線、反射鏡設置或移除",
        "交通號誌、標誌、標線及大眾運輸_其他",
        "交通號誌增設或紅綠燈秒數調整",
        "路邊停車格問題",
        "公共自行車(U-Bike)租賃問題",
        "交通號誌(紅綠燈)故障或損壞傾斜",
        "交通標誌牌面、反射鏡損壞傾斜",
        "捷運營運及管理",
        "捷運建設工程相關問題",
        "公車動態系統問題",
        "停車費問題",
        "計程車問題及招呼站設施管理",
        "免費市民公車(樂活巴)問題",
    ],
    "路霸排除": [
        "占用道路、騎樓及人行道",
        "無牌廢棄車查報",
        "有牌廢棄車查報",
        "路霸排除_其他",
        "廣告車輛長期占用停車格",
    ],
    "噪音、污染及環境維護": [
        "髒亂點查報",
        "住家、改裝車噪音",
        "綜合性環境污染",
        "噪音、污染及環境維護_其他",
        "營業場所、工廠及施工噪音",
        "空氣污染",
        "住宅內人與動物噪音",
        "垃圾車清運動線及管理",
        "工廠排放廢水、河川污染",
        "違規張貼廣告物",
        "廢棄物清運預約",
        "犬貓屍體清除",
    ],
    "其他類別": ["其他建議、諮詢或陳情", "其他檢舉案件"],
    "警政及交通裁罰業務": [
        "警政及交通裁罰業務_其他",
        "警政風紀",
        "交通罰單申訴",
        "交通疏導或壅塞通報",
        "治安維護",
        "妨害風化(俗)",
        "闖紅燈(超速)照相桿增設或維護",
        "監視器問題",
        "車輛拖吊爭議",
    ],
    "建築管理": [
        "公寓大廈管理問題",
        "一般違建查報",
        "建築物公共安全問題",
        "建築管理_其他",
        "興建中違建查報",
        "建築法規問題",
        "違規招牌或樹立廣告物查報",
        "社會住宅管理",
        "領有建造執照施工損鄰",
        "已查報違建拆除問題",
    ],
    "道路、水溝維護": [
        "路面不平整或掏空破洞",
        "道路、水溝維護_其他",
        "道路施工時間、交通管制及安全管理問題",
        "道路側溝清淤或惡臭處理",
        "水溝溝蓋維修",
        "道路淹(積)水通報",
        "孔蓋異音",
        "電纜下地或纜線垂落",
        "路面油漬清除",
    ],
    "路燈、路樹及公園管理維護": [
        "路燈、路樹及公園管理維護_其他",
        "公園、綠地及路樹養護",
        "路燈故障",
        "公園設施損壞",
        "路樹傾倒",
        "路燈新增或遷移申請",
        "新闢公園建議案",
    ],
    "衛生行政": [
        "食品安全衛生",
        "藥品及化妝品管理",
        "醫療管理",
        "菸害防制",
        "衛生行政_其他",
        "傳染病防治及預防接種",
        "自殺防治及心理健康",
    ],
    "教育及體育": [
        "國小學校問題",
        "國中學校問題",
        "教育及體育_其他",
        "高級中等學校問題",
        "補教問題",
        "體育活動及場務管理",
        "幼兒園問題",
        "特殊教育問題",
        "教師介聘甄選",
        "社區大學、樂齡學習等終身教育問題",
        "學校體育問題",
    ],
    "勞動行政": [
        "檢舉公司(雇主)違反勞動法規",
        "勞工法令諮詢",
        "勞動行政_其他",
        "勞資糾紛協調",
        "移工業務",
        "就業服務及職業訓練",
        "就業歧視",
        "身障就業",
    ],
    "工商、經濟及稅務": [
        "檢舉商店違規營業",
        "水、電、瓦斯等公用事業問題",
        "稅務問題",
        "工商、經濟及稅務_其他",
        "檢舉工廠違規營業",
        "市場攤販管理",
        "工商登記問題",
        "檢舉旅館、民宿違規營業",
    ],
    "社會救助及社會福利": [
        "社會救助及社會福利_其他",
        "身心障礙福利及復康巴士",
        "銀髮族福利、長期照顧及日間照顧",
        "社會救助(中、低收入戶、急難救助及馬上關懷等)",
        "婦女福利、特殊境遇家庭扶助、生育津貼及育兒津貼",
        "婦女(幼)館、親子館及公設民營托嬰中心管理",
        "兒少福利、兒童早療補助、弱勢兒少生活扶助、緊急生活扶助及醫療補助",
        "家庭暴力、性侵害、兒少保護及性騷擾等防治工作",
        "住宅租金補貼問題",
        "人民團體組織輔導",
        "家庭服務中心",
    ],
    "地政服務": [
        "檢舉土地違規使用",
        "地政服務_其他",
        "不動產交易",
        "土地徵收",
        "土地及建物登記",
        "土地重劃",
        "土地測量",
        "地籍圖重測",
    ],
    "消防行政": [
        "消防設備、安全檢查",
        "防火巷違建、堆放雜物",
        "消防行政_其他",
        "瓦斯桶儲放問題",
        "消防栓(設置、移位、告示牌)",
    ],
    "感謝函、服務品質及網站、APP管理問題": [
        "感謝函",
        "市府網站或APP管理問題",
        "服務態度問題",
        "行政效率問題",
        "專業知識問題",
        "感謝函、服務品質及網站、APP管理問題_其他",
    ],
    "動物收容、保護及捕捉": [
        "動物收容、保護及捕捉_其他",
        "動物收容及認養問題",
        "動物受困、受傷通報",
        "捕蜂、抓蛇",
    ],
    "文化藝術及圖書管理": [
        "圖書館、閱覽室及館舍管理",
        "藝文展演活動",
        "文化藝術及圖書管理_其他",
        "藝文館舍管理",
        "文化資產問題",
    ],
    "民政業務": ["戶政服務", "民政業務_其他", "宗教事務", "殯葬禮儀", "兵役問題"],
    "政風行政": ["行政違失", "其他瀆職情形", "行、收賄"],
    "市民卡業務": [
        "市民卡業務_其他",
        "市民卡優惠及加值服務建議",
        "卡片感應及使用問題",
        "學生卡申辦問題",
        "一般卡申辦問題",
        "行動卡、聯名卡申辦問題",
        "志工卡申辦問題",
        "敬老卡申辦問題",
    ],
}


# ===== 2) 解析 XML 的小工具（保持不變） =====
def parse_category_xml(xml_text: str) -> Dict[str, str]:
    root = ET.fromstring(xml_text)
    items: Dict[str, str] = {}
    for el in root.findall(".//category"):
        name = (el.get("name") or "").strip()
        desc_el = el.find("description")
        desc = (desc_el.text or "").strip() if desc_el is not None else ""
        if name:
            items[name] = desc
    return items


# ===== 3) 組裝 JSON（保持不變） =====
def build_category_json(
    knowledge: Dict[str, List[str]],
    main_defs: Dict[str, str],
    sub_defs: Dict[str, str],
    missing_placeholder: str = "（未提供定義）",
) -> Tuple[Dict[str, Any], Dict[str, List[str]]]:
    result: Dict[str, Any] = {}
    warnings = {
        "main_without_definition": [],
        "sub_without_definition": [],
        "extra_mains_in_xml": [],
        "extra_subs_in_xml": [],
    }

    for main, sub_list in knowledge.items():
        main_desc = main_defs.get(main, missing_placeholder)
        if main not in main_defs:
            warnings["main_without_definition"].append(main)
        result[main] = {"description": main_desc, "sub_categories": {}}
        for sub in sub_list:
            sub_desc = sub_defs.get(sub, missing_placeholder)
            if sub not in sub_defs:
                warnings["sub_without_definition"].append(sub)
            result[main]["sub_categories"][sub] = {"description": sub_desc}

    extra_mains = [m for m in main_defs.keys() if m not in knowledge]
    if extra_mains:
        warnings["extra_mains_in_xml"] = extra_mains

    known_subs = set()
    for subs in knowledge.values():
        known_subs.update(subs)
    extra_subs = [s for s in sub_defs.keys() if s not in known_subs]
    if extra_subs:
        warnings["extra_subs_in_xml"] = extra_subs

    return result, warnings


# ===== 4) 主程式：改為讀檔 =====
def read_text(path: str) -> str:
    # 使用 utf-8-sig 可自動去除 BOM
    with open(path, "r", encoding="utf-8-sig") as f:
        return f.read()


if __name__ == "__main__":
    ap = argparse.ArgumentParser(description="Convert main/sub category XML to JSON")
    ap.add_argument(
        "-m",
        "--main-xml",
        required=True,
        help="主案類定義 XML 檔路徑（<main_category_definitions>）",
    )
    ap.add_argument(
        "-s",
        "--sub-xml",
        required=True,
        help="子案類定義 XML 檔路徑（<sub_category_definitions>）",
    )
    ap.add_argument(
        "-o",
        "--out",
        default="-",
        help="輸出 JSON 檔路徑（預設輸出到 stdout；使用 '-' 亦為 stdout）",
    )
    ap.add_argument(
        "--print-warnings", action="store_true", help="在 stderr 輸出對照不一致警告"
    )
    args = ap.parse_args()

    try:
        main_defs = parse_category_xml(read_text(args.main_xml))
    except FileNotFoundError:
        sys.exit(f"找不到主案類 XML 檔案：{args.main_xml}")
    except ET.ParseError as e:
        sys.exit(f"主案類 XML 解析錯誤：{e}")

    try:
        sub_defs = parse_category_xml(read_text(args.sub_xml))
    except FileNotFoundError:
        sys.exit(f"找不到子案類 XML 檔案：{args.sub_xml}")
    except ET.ParseError as e:
        sys.exit(f"子案類 XML 解析錯誤：{e}")

    data, warns = build_category_json(category_knowledge, main_defs, sub_defs)
    out_json = json.dumps(data, ensure_ascii=False, indent=2)

    if args.out == "-" or args.out == "":
        print(out_json)
    else:
        with open(args.out, "w", encoding="utf-8") as f:
            f.write(out_json)

    if args.print_warnings:
        print(json.dumps(warns, ensure_ascii=False, indent=2), file=sys.stderr)
