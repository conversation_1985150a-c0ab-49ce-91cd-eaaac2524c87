version: '3.8'

services:
  complaint-classifier:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: complaint-classifier
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data:ro
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import asyncio; from src.complaint_classifier import ComplaintClassifier; asyncio.run(ComplaintClassifier().validate_setup())"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Redis for caching (if needed in future)
  redis:
    image: redis:7-alpine
    container_name: complaint-classifier-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  redis_data:
