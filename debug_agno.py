#!/usr/bin/env python3
"""Debug script to test agno response structure."""

import asyncio
import os

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from dotenv import load_dotenv

load_dotenv()


async def test_agno_response():
    """Test agno response structure."""
    try:
        # Create a simple agent
        agent = Agent(
            name="TestAgent",
            model=OpenAIChat(
                id="gpt-3.5-turbo", temperature=0.1, api_key=os.getenv("OPENAI_API_KEY")
            ),
            instructions=[
                "You are a helpful assistant. Always respond with valid JSON."
            ],
        )

        # Test with a simple prompt
        prompt = 'Please respond with this JSON: {"test": "hello", "status": "ok"}'
        response = await agent.arun(prompt)

        print(f"Response type: {type(response)}")
        print(f"Response attributes: {dir(response)}")
        print(
            f"Response content: {response.content if hasattr(response, 'content') else 'No content attr'}"
        )
        print(f"Response: {response}")

        # Try to access content in different ways
        if hasattr(response, "content"):
            print(f"Content type: {type(response.content)}")
            print(f"Content value: '{response.content}'")
            print(f"Content repr: {repr(response.content)}")

        return response

    except Exception as e:
        print(f"Error: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_agno_response())
