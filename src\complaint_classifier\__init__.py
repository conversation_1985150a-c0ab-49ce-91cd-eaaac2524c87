"""
AI Multi-Agent System for Citizen Complaint Classification

This package implements a sophisticated multi-agent system for classifying
citizen complaint cases using the Agno framework.

The system follows a "Expert Committee" approach with the following agents:
- Triage Agent: Fast initial classification and routing
- Orchestrator Agent: Workflow coordination and decision making
- Main Category Analyzer: High-level semantic understanding
- Sub Category Analyzer: Fine-grained classification
- Review Agent: Quality assurance and validation
- Output Agent: Final result formatting

Architecture:
- Fast Track: Simple cases handled directly by Triage Agent
- Expert Review Path: Complex cases go through full expert committee
"""

__version__ = "0.1.0"
__author__ = "AI Application Team"

from .agents import *
from .classifier import ComplaintClassifier
from .knowledge import *
from .workflow import *

__all__ = [
    "TriageAgent",
    "OrchestratorAgent",
    "MainCategoryAnalyzer",
    "SubCategoryAnalyzer",
    "ReviewAgent",
    "OutputAgent",
    "KnowledgeManager",
    "WorkflowEngine",
    "ComplaintClassifier",
]
