"""
Orchestrator Agent for coordinating the multi-agent workflow.
"""

import logging
from typing import Any, Dict, List

from .base import BaseClassificationAgent
from ..models import ComplaintCase, ClassificationResult
from ..config import ModelConfig

logger = logging.getLogger(__name__)


class OrchestratorAgent(BaseClassificationAgent):
    """
    Orchestrator Agent - Workflow coordination specialist.
    
    Responsibilities:
    - Coordinate the overall classification workflow
    - Manage agent interactions and data flow
    - Handle workflow decisions and routing
    - Ensure proper error handling and fallbacks
    """
    
    def __init__(self, model_config: ModelConfig):
        """Initialize the Orchestrator Agent.
        
        Args:
            model_config: Configuration for the AI model
        """
        super().__init__(
            model_config=model_config,
            name="Orchestrator Agent",
            role="Workflow coordination and management specialist"
        )
        self.reasoning_chain: List[Dict[str, Any]] = []
    
    def _get_system_instructions(self) -> list[str]:
        """Get system instructions for the Orchestrator Agent."""
        return [
            "你是工作流程協調專員，負責管理整個分類流程的執行。",
            "你的任務是確保各個 Agent 按正確順序執行，並處理異常情況。",
            "你需要記錄整個推理鏈，以便追蹤決策過程。",
            "你負責在必要時進行重試和錯誤恢復。"
        ]
    
    def _create_agent(self):
        """Create the Orchestrator Agent (primarily logic-based)."""
        # The orchestrator is primarily logic-based and may not need an LLM
        # for basic workflow coordination
        return None
    
    async def _process_internal(self, complaint: ComplaintCase, **kwargs) -> ClassificationResult:
        """Process complaint through the complete workflow.
        
        Args:
            complaint: The complaint case to process
            **kwargs: Additional arguments including agent instances
            
        Returns:
            Final classification result
        """
        # This method will be implemented in the workflow engine
        # The orchestrator coordinates but doesn't directly process
        raise NotImplementedError("Orchestrator processing is handled by WorkflowEngine")
    
    def add_reasoning_step(self, agent_name: str, input_data: Any, output_data: Any, metadata: Dict[str, Any] = None):
        """Add a step to the reasoning chain.
        
        Args:
            agent_name: Name of the agent that performed the step
            input_data: Input data for the step
            output_data: Output data from the step
            metadata: Additional metadata for the step
        """
        step = {
            "agent": agent_name,
            "input": self._serialize_data(input_data),
            "output": self._serialize_data(output_data),
            "metadata": metadata or {}
        }
        self.reasoning_chain.append(step)
        logger.debug(f"Added reasoning step for {agent_name}")
    
    def get_reasoning_chain(self) -> List[Dict[str, Any]]:
        """Get the complete reasoning chain.
        
        Returns:
            List of reasoning steps
        """
        return self.reasoning_chain.copy()
    
    def clear_reasoning_chain(self):
        """Clear the reasoning chain for a new classification."""
        self.reasoning_chain.clear()
        logger.debug("Cleared reasoning chain")
    
    def _serialize_data(self, data: Any) -> Any:
        """Serialize data for storage in reasoning chain.
        
        Args:
            data: Data to serialize
            
        Returns:
            Serialized data
        """
        if hasattr(data, 'model_dump'):
            # Pydantic model
            return data.model_dump()
        elif isinstance(data, dict):
            return data
        elif isinstance(data, (str, int, float, bool)):
            return data
        elif isinstance(data, list):
            return [self._serialize_data(item) for item in data]
        else:
            # Convert to string for other types
            return str(data)
    
    def handle_error(self, agent_name: str, error: Exception, input_data: Any) -> Dict[str, Any]:
        """Handle errors from agents.
        
        Args:
            agent_name: Name of the agent that encountered the error
            error: The exception that occurred
            input_data: Input data that caused the error
            
        Returns:
            Error information dictionary
        """
        error_info = {
            "agent": agent_name,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "input_data": self._serialize_data(input_data)
        }
        
        self.add_reasoning_step(
            agent_name=agent_name,
            input_data=input_data,
            output_data={"error": error_info},
            metadata={"status": "error"}
        )
        
        logger.error(f"Error in {agent_name}: {error}")
        return error_info
