"""
Basic usage example for the AI Multi-Agent Complaint Classification System.

This example demonstrates how to use the system to classify citizen complaints.
"""

import asyncio
import os
from pathlib import Path

# Load environment variables from .env file
from dotenv import load_dotenv

load_dotenv()

# Add the src directory to the path so we can import our modules
import sys

sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from complaint_classifier import ComplaintClassifier
from complaint_classifier.config import Settings


async def main():
    """Main example function."""

    print("🤖 AI Multi-Agent Complaint Classification System")
    print("=" * 50)

    # Show environment loading status
    print("🔧 Loading environment variables from .env file...")
    env_file_path = Path(__file__).parent.parent / ".env"
    if env_file_path.exists():
        print(f"   ✅ Found .env file at: {env_file_path}")
    else:
        print(f"   ⚠️  .env file not found at: {env_file_path}")
        print("   📝 You can copy .env.example to .env and set your API keys")

    # Initialize the classifier
    print("📋 Initializing classifier...")
    settings = Settings()
    classifier = ComplaintClassifier(settings)

    # Validate system setup
    print("🔍 Validating system setup...")
    validation = await classifier.validate_setup()

    print("\nValidation Results:")
    for component, status in validation.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {component.replace('_', ' ').title()}")

    if not all(validation.values()):
        print("\n⚠️  System validation failed. Please check your configuration.")
        if not validation.get("configuration", False):
            print(
                "   - Make sure to set OPENAI_API_KEY or ANTHROPIC_API_KEY in your environment"
            )
        return

    # Show system statistics
    print("\n📊 System Statistics:")
    stats = classifier.get_statistics()
    kb_stats = stats["knowledge_base"]
    print(
        f"  📚 Knowledge Base: {kb_stats['main_categories']} main categories, {kb_stats['total_sub_categories']} sub-categories"
    )
    print(
        f"  ⚡ Fast Track: {'Enabled' if stats['configuration']['fast_track_enabled'] else 'Disabled'}"
    )

    # Example complaints to classify
    example_complaints = [
        {
            "content": "路燈不亮了，晚上很危險，請派人修理",
            "case_id": "EXAMPLE_001",
            "description": "Simple street light issue",
        },
        {
            "content": "我們社區的路面有很多坑洞，騎車很危險，而且下雨時會積水",
            "case_id": "EXAMPLE_002",
            "description": "Road maintenance issue",
        },
        {
            "content": """
            我住在市中心，最近發現我們社區附近有很多問題：
            1. 路燈經常不亮，晚上很危險
            2. 路面有很多坑洞，騎車很危險
            3. 垃圾車來的時間不固定
            4. 附近有工地噪音很大
            請政府幫忙解決這些問題。
            """,
            "case_id": "EXAMPLE_003",
            "description": "Complex multi-issue complaint",
        },
    ]

    print("\n🔄 Processing Example Complaints:")
    print("=" * 50)

    for i, complaint in enumerate(example_complaints, 1):
        print(f"\n📝 Example {i}: {complaint['description']}")
        print(f"Case ID: {complaint['case_id']}")
        print(
            f"Content: {complaint['content'][:100]}{'...' if len(complaint['content']) > 100 else ''}"
        )

        try:
            # Classify the complaint
            print("🤔 Classifying...")
            result = await classifier.classify(
                content=complaint["content"], case_id=complaint["case_id"]
            )

            # Display results
            print("✅ Result:")
            print(f"   📂 Category: {result.category}")
            print(
                f"   🎯 Confidence: {result.confidence:.2f}"
                if result.confidence
                else "   🎯 Confidence: N/A"
            )
            print(f"   🛤️  Path: {result.path_taken.replace('_', ' ').title()}")
            print(
                f"   ✔️  Review: {'Passed' if result.review_passed else 'Failed' if result.review_passed is not None else 'N/A'}"
            )

            # Show reasoning chain summary
            if result.reasoning_chain:
                print(f"   🧠 Reasoning Steps: {len(result.reasoning_chain)}")
                for j, step in enumerate(result.reasoning_chain, 1):
                    agent = step.get("agent", "Unknown")
                    metadata = step.get("metadata", {})
                    step_type = metadata.get("step", "unknown")
                    print(f"      {j}. {agent} ({step_type})")

        except Exception as e:
            print(f"❌ Error: {e}")

        print("-" * 30)

    print("\n🎉 Example completed!")
    print("\nNext steps:")
    print("1. Set up your API keys in environment variables")
    print(
        "2. Try the CLI tool: python -m complaint_classifier.cli classify 'your complaint text'"
    )
    print("3. Integrate the classifier into your application")


if __name__ == "__main__":
    # Check if API keys are available
    has_openai = bool(os.getenv("OPENAI_API_KEY"))
    has_anthropic = bool(os.getenv("ANTHROPIC_API_KEY"))

    if not (has_openai or has_anthropic):
        print("⚠️  No API keys found in environment variables.")
        print("Please set OPENAI_API_KEY or ANTHROPIC_API_KEY to run this example.")
        print("\nExample:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        print("python examples/basic_usage.py")
        exit(1)

    # Run the example
    asyncio.run(main())
