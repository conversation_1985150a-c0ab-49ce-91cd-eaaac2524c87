"""
Data models for the knowledge management system.
"""

from typing import Dict, List, Optional
from pydantic import BaseModel, Field


class SubCategoryDefinition(BaseModel):
    """Definition of a sub-category."""
    description: str = Field(..., description="Detailed description of the sub-category")


class CategoryDefinition(BaseModel):
    """Definition of a main category with its sub-categories."""
    description: str = Field(..., description="Description of the main category")
    sub_categories: Dict[str, SubCategoryDefinition] = Field(
        default_factory=dict,
        description="Dictionary of sub-category name to definition"
    )


class KnowledgeBase(BaseModel):
    """Complete knowledge base containing all categories."""
    categories: Dict[str, CategoryDefinition] = Field(
        default_factory=dict,
        description="Dictionary of main category name to definition"
    )
    
    def get_main_categories(self) -> List[str]:
        """Get list of all main category names."""
        return list(self.categories.keys())
    
    def get_sub_categories(self, main_category: str) -> List[str]:
        """Get list of sub-category names for a given main category."""
        if main_category not in self.categories:
            return []
        return list(self.categories[main_category].sub_categories.keys())
    
    def get_main_category_description(self, main_category: str) -> Optional[str]:
        """Get description of a main category."""
        if main_category not in self.categories:
            return None
        return self.categories[main_category].description
    
    def get_sub_category_description(self, main_category: str, sub_category: str) -> Optional[str]:
        """Get description of a sub-category."""
        if main_category not in self.categories:
            return None
        if sub_category not in self.categories[main_category].sub_categories:
            return None
        return self.categories[main_category].sub_categories[sub_category].description
    
    def validate_category_path(self, main_category: str, sub_category: str) -> bool:
        """Validate that a sub-category belongs to the given main category."""
        if main_category not in self.categories:
            return False
        return sub_category in self.categories[main_category].sub_categories


class ContextData(BaseModel):
    """Context data for agent prompts."""
    main_categories: Optional[Dict[str, str]] = None
    sub_categories: Optional[Dict[str, str]] = None
    selected_main_category: Optional[str] = None
    selected_sub_category: Optional[str] = None
