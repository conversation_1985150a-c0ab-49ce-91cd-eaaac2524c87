"""
示範如何使用 python-dotenv 載入環境變數的範例。

這個範例展示了：
1. 如何載入 .env 檔案
2. 如何讀取環境變數
3. 如何設定預設值
4. 如何驗證必要的環境變數
"""

import os
from pathlib import Path

from dotenv import dotenv_values, load_dotenv


def demonstrate_basic_usage():
    """基本的 dotenv 使用方式"""
    print("🔧 基本 python-dotenv 使用方式")
    print("=" * 40)

    # 方法 1: 載入專案根目錄的 .env 檔案
    env_path = Path(__file__).parent.parent / ".env"
    print(f"📁 載入 .env 檔案: {env_path}")

    if env_path.exists():
        load_dotenv(env_path)
        print("   ✅ .env 檔案載入成功")
    else:
        print("   ⚠️  .env 檔案不存在")
        return

    # 方法 2: 自動尋找 .env 檔案
    # load_dotenv(find_dotenv())

    print("\n📋 讀取環境變數:")

    # 讀取 API 金鑰
    openai_key = os.getenv("OPENAI_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")

    print(
        f"   🔑 OPENAI_API_KEY: {'已設定' if openai_key and openai_key != 'your_openai_api_key_here' else '未設定或使用預設值'}"
    )
    print(
        f"   🔑 ANTHROPIC_API_KEY: {'已設定' if anthropic_key and anthropic_key != 'your_anthropic_api_key_here' else '未設定或使用預設值'}"
    )

    # 讀取應用程式設定（帶預設值）
    environment = os.getenv("ENVIRONMENT", "development")
    debug = os.getenv("DEBUG", "false").lower() == "true"
    log_level = os.getenv("LOG_LEVEL", "INFO")

    print(f"   🌍 ENVIRONMENT: {environment}")
    print(f"   🐛 DEBUG: {debug}")
    print(f"   📊 LOG_LEVEL: {log_level}")

    # 讀取工作流配置（嵌套配置）
    triage_threshold = float(os.getenv("WORKFLOW__TRIAGE_CONFIDENCE_THRESHOLD", "0.85"))
    enable_fast_track = (
        os.getenv("WORKFLOW__ENABLE_FAST_TRACK", "true").lower() == "true"
    )

    print(f"   🎯 WORKFLOW__TRIAGE_CONFIDENCE_THRESHOLD: {triage_threshold}")
    print(f"   ⚡ WORKFLOW__ENABLE_FAST_TRACK: {enable_fast_track}")

    # 讀取代理模型配置（嵌套配置）
    triage_model_provider = os.getenv("AGENTS__TRIAGE_MODEL__PROVIDER", "openai")
    triage_model_id = os.getenv("AGENTS__TRIAGE_MODEL__MODEL_ID", "gpt-4o-mini")

    print(f"   🤖 AGENTS__TRIAGE_MODEL__PROVIDER: {triage_model_provider}")
    print(f"   🧠 AGENTS__TRIAGE_MODEL__MODEL_ID: {triage_model_id}")


def demonstrate_dotenv_values():
    """使用 dotenv_values 讀取所有變數"""
    print("\n🔍 使用 dotenv_values 讀取所有變數")
    print("=" * 40)

    env_path = Path(__file__).parent.parent / ".env"

    if not env_path.exists():
        print("   ⚠️  .env 檔案不存在")
        return

    # 讀取所有環境變數到字典
    config = dotenv_values(env_path)

    print("📋 .env 檔案中的所有變數:")
    for key, value in config.items():
        if "API_KEY" in key:
            # 隱藏 API 金鑰的實際值
            display_value = (
                "***已設定***"
                if value and value != f"your_{key.lower()}_here"
                else "未設定"
            )
        else:
            display_value = value
        print(f"   {key}: {display_value}")


def validate_required_env_vars():
    """驗證必要的環境變數"""
    print("\n✅ 驗證必要的環境變數")
    print("=" * 40)

    required_vars = ["OPENAI_API_KEY", "ANTHROPIC_API_KEY"]
    optional_vars = ["ENVIRONMENT", "DEBUG", "LOG_LEVEL"]

    print("🔍 檢查必要變數:")
    all_valid = True

    for var in required_vars:
        value = os.getenv(var)
        is_set = value and value != f"your_{var.lower()}_here"
        status = "✅" if is_set else "❌"
        print(f"   {status} {var}: {'已設定' if is_set else '未設定'}")
        if not is_set:
            all_valid = False

    print("\n🔧 檢查選用變數:")
    for var in optional_vars:
        value = os.getenv(var)
        status = "✅" if value else "⚪"
        print(f"   {status} {var}: {value if value else '使用預設值'}")

    print(f"\n🎯 整體狀態: {'✅ 可以使用' if all_valid else '❌ 需要設定 API 金鑰'}")

    if not all_valid:
        print("\n💡 設定步驟:")
        print("1. 編輯 .env 檔案")
        print("2. 將 'your_openai_api_key_here' 替換為您的實際 OpenAI API 金鑰")
        print("3. 或將 'your_anthropic_api_key_here' 替換為您的實際 Anthropic API 金鑰")
        print("4. 至少需要設定其中一個 API 金鑰")


def main():
    """主函數"""
    print("🐍 Python-dotenv 使用示範")
    print("=" * 50)

    # 載入環境變數
    env_path = Path(__file__).parent.parent / ".env"
    load_dotenv(env_path)

    # 示範各種用法
    demonstrate_basic_usage()
    demonstrate_dotenv_values()
    validate_required_env_vars()

    print("\n🎉 示範完成！")
    print("\n📚 更多 python-dotenv 功能:")
    print("- load_dotenv(override=True)  # 覆蓋現有環境變數")
    print("- load_dotenv(verbose=True)   # 顯示載入過程")
    print("- find_dotenv()               # 自動尋找 .env 檔案")
    print("- dotenv_values()             # 只讀取不設定環境變數")


if __name__ == "__main__":
    main()
