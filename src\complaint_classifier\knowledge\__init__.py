"""
Knowledge management system for complaint classification.

This module handles:
- Loading and parsing the knowledge base (categories_def.json)
- Dynamic context injection for different agents
- Intelligent context management to minimize token usage
- Knowledge base validation and consistency checks
"""

from .manager import KnowledgeManager
from .models import CategoryDefinition, KnowledgeBase
from .context_injector import ContextInjector

__all__ = [
    "KnowledgeManager",
    "CategoryDefinition", 
    "KnowledgeBase",
    "ContextInjector",
]
