"""
Context injector for dynamically creating agent prompts with relevant knowledge.
"""

import logging
from typing import Dict, List, Optional

from .manager import <PERSON>Manager
from .models import ContextData

logger = logging.getLogger(__name__)


class ContextInjector:
    """Handles intelligent context injection for different agents."""
    
    def __init__(self, knowledge_manager: KnowledgeManager):
        """Initialize the context injector.
        
        Args:
            knowledge_manager: Knowledge manager instance
        """
        self.knowledge_manager = knowledge_manager
    
    def create_main_category_context(self) -> ContextData:
        """Create context for Main Category Analyzer Agent.
        
        Returns:
            Context data with main category definitions
        """
        main_categories = self.knowledge_manager.get_main_category_definitions()
        
        logger.debug(f"Created main category context with {len(main_categories)} categories")
        
        return ContextData(main_categories=main_categories)
    
    def create_sub_category_context(self, main_category: str) -> ContextData:
        """Create context for Sub Category Analyzer Agent.
        
        Args:
            main_category: The main category to get sub-categories for
            
        Returns:
            Context data with sub-category definitions for the specified main category
        """
        sub_categories = self.knowledge_manager.get_sub_category_definitions(main_category)
        
        logger.debug(f"Created sub-category context for '{main_category}' with {len(sub_categories)} sub-categories")
        
        return ContextData(
            sub_categories=sub_categories,
            selected_main_category=main_category
        )
    
    def create_review_context(self, main_category: str, sub_category: str) -> ContextData:
        """Create context for Review Agent.
        
        Args:
            main_category: The selected main category
            sub_category: The selected sub-category
            
        Returns:
            Context data with both main and sub-category definitions
        """
        # Get the specific definitions for validation
        main_cat_desc = self.knowledge_manager.knowledge_base.get_main_category_description(main_category)
        sub_cat_desc = self.knowledge_manager.knowledge_base.get_sub_category_description(main_category, sub_category)
        
        main_categories = {main_category: main_cat_desc} if main_cat_desc else {}
        sub_categories = {sub_category: sub_cat_desc} if sub_cat_desc else {}
        
        logger.debug(f"Created review context for '{main_category}' -> '{sub_category}'")
        
        return ContextData(
            main_categories=main_categories,
            sub_categories=sub_categories,
            selected_main_category=main_category,
            selected_sub_category=sub_category
        )
    
    def format_main_category_prompt_section(self, context: ContextData) -> str:
        """Format main category definitions for prompt injection.
        
        Args:
            context: Context data with main categories
            
        Returns:
            Formatted string for prompt injection
        """
        if not context.main_categories:
            return ""
        
        sections = []
        sections.append("**主案類清單與定義:**")
        
        for name, description in context.main_categories.items():
            sections.append(f"- **{name}**: {description}")
        
        return "\n".join(sections)
    
    def format_sub_category_prompt_section(self, context: ContextData) -> str:
        """Format sub-category definitions for prompt injection.
        
        Args:
            context: Context data with sub-categories
            
        Returns:
            Formatted string for prompt injection
        """
        if not context.sub_categories or not context.selected_main_category:
            return ""
        
        sections = []
        sections.append(f"**【{context.selected_main_category}】子案類清單與定義:**")
        
        for name, description in context.sub_categories.items():
            sections.append(f"- {name}: {description}")
        
        return "\n".join(sections)
    
    def format_review_prompt_section(self, context: ContextData) -> str:
        """Format definitions for review agent prompt injection.
        
        Args:
            context: Context data with selected categories
            
        Returns:
            Formatted string for prompt injection
        """
        sections = []
        
        if context.main_categories and context.selected_main_category:
            main_desc = context.main_categories.get(context.selected_main_category, "")
            sections.append("**官方定義查核:**")
            sections.append(f"- **主分類定義 ('{context.selected_main_category}'):** '{main_desc}'")
        
        if context.sub_categories and context.selected_sub_category:
            sub_desc = context.sub_categories.get(context.selected_sub_category, "")
            sections.append(f"- **子分類定義 ('{context.selected_sub_category}'):** '{sub_desc}'")
        
        return "\n".join(sections)
