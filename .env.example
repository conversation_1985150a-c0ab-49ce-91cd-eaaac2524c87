# AI Model API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Application Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Knowledge Base
KNOWLEDGE_BASE_PATH=src/data/categories_def.json

# Workflow Configuration
TRIAGE_CONFIDENCE_THRESHOLD=0.85
PARALLEL_ANALYSIS_THRESHOLD=0.15
ENABLE_FAST_TRACK=true
FALLBACK_CATEGORY=其他建議、諮詢或陳情

# Agent Model Configuration
TRIAGE_MODEL_PROVIDER=openai
TRIAGE_MODEL_ID=gpt-4o-mini
MAIN_ANALYZER_MODEL_PROVIDER=openai
MAIN_ANALYZER_MODEL_ID=gpt-4o
SUB_ANALYZER_MODEL_PROVIDER=openai
SUB_ANALYZER_MODEL_ID=gpt-4o
REVIEW_MODEL_PROVIDER=openai
REVIEW_MODEL_ID=gpt-4o
ORCHESTRATOR_MODEL_PROVIDER=openai
ORCHESTRATOR_MODEL_ID=gpt-4o
