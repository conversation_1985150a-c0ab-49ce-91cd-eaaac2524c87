"""
Base agent class for the complaint classification system.
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.anthropic import Claude

from ..config import ModelConfig

logger = logging.getLogger(__name__)


class BaseClassificationAgent(ABC):
    """Base class for all classification agents."""
    
    def __init__(self, model_config: ModelConfig, name: str, role: str):
        """Initialize the base agent.
        
        Args:
            model_config: Configuration for the AI model
            name: Name of the agent
            role: Role description of the agent
        """
        self.model_config = model_config
        self.name = name
        self.role = role
        self._agent: Optional[Agent] = None
        
    def _create_model(self):
        """Create the appropriate model based on configuration."""
        if self.model_config.provider.lower() == "openai":
            return OpenAIChat(
                id=self.model_config.model_id,
                temperature=self.model_config.temperature,
                max_tokens=self.model_config.max_tokens,
                api_key=self.model_config.api_key
            )
        elif self.model_config.provider.lower() == "anthropic":
            return Claude(
                id=self.model_config.model_id,
                temperature=self.model_config.temperature,
                max_tokens=self.model_config.max_tokens,
                api_key=self.model_config.api_key
            )
        else:
            raise ValueError(f"Unsupported model provider: {self.model_config.provider}")
    
    @property
    def agent(self) -> Agent:
        """Get the Agno agent instance, creating it if necessary."""
        if self._agent is None:
            self._agent = self._create_agent()
        return self._agent
    
    @abstractmethod
    def _create_agent(self) -> Agent:
        """Create the Agno agent with specific configuration.
        
        Returns:
            Configured Agno agent
        """
        pass
    
    @abstractmethod
    def _get_system_instructions(self) -> list[str]:
        """Get system instructions for this agent.
        
        Returns:
            List of instruction strings
        """
        pass
    
    def _create_base_agent(self, additional_instructions: Optional[list[str]] = None) -> Agent:
        """Create a base Agno agent with common configuration.
        
        Args:
            additional_instructions: Additional instructions specific to the agent
            
        Returns:
            Configured Agno agent
        """
        instructions = self._get_system_instructions()
        if additional_instructions:
            instructions.extend(additional_instructions)
        
        model = self._create_model()
        
        return Agent(
            name=self.name,
            role=self.role,
            model=model,
            instructions=instructions,
            markdown=True,
            show_tool_calls=False,
            structured_outputs=True
        )
    
    async def process(self, input_data: Any, **kwargs) -> Any:
        """Process input data and return result.
        
        Args:
            input_data: Input data to process
            **kwargs: Additional keyword arguments
            
        Returns:
            Processing result
        """
        try:
            logger.debug(f"{self.name} processing input")
            result = await self._process_internal(input_data, **kwargs)
            logger.debug(f"{self.name} completed processing")
            return result
        except Exception as e:
            logger.error(f"Error in {self.name}: {e}")
            raise
    
    @abstractmethod
    async def _process_internal(self, input_data: Any, **kwargs) -> Any:
        """Internal processing method to be implemented by subclasses.
        
        Args:
            input_data: Input data to process
            **kwargs: Additional keyword arguments
            
        Returns:
            Processing result
        """
        pass
