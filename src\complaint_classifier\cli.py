"""
Command-line interface for the complaint classifier.
"""

import asyncio
from typing import Optional

import typer
from dotenv import load_dotenv
from rich.console import Console
from rich.json import JSON
from rich.panel import Panel
from rich.table import Table

from .classifier import ComplaintClassifier
from .config import Settings

app = typer.Typer(help="AI Multi-Agent Complaint Classification System")
console = Console()


@app.command()
def classify(
    text: str = typer.Argument(..., help="Complaint text to classify"),
    case_id: Optional[str] = typer.Option(None, "--case-id", "-c", help="Case ID"),
    output_format: str = typer.Option(
        "table", "--format", "-f", help="Output format: table, json"
    ),
    verbose: bool = typer.Option(
        False, "--verbose", "-v", help="Show detailed reasoning chain"
    ),
):
    """Classify a citizen complaint."""

    async def _classify():
        try:
            # Initialize classifier
            settings = Settings()
            classifier = ComplaintClassifier(settings)

            # Validate setup
            validation = await classifier.validate_setup()
            if not all(validation.values()):
                console.print("[red]System validation failed:[/red]")
                for component, status in validation.items():
                    status_icon = "✓" if status else "✗"
                    color = "green" if status else "red"
                    console.print(f"  {status_icon} {component}", style=color)
                return

            # Classify the complaint
            console.print("[blue]Classifying complaint...[/blue]")
            result = await classifier.classify(text, case_id=case_id)

            # Display results
            if output_format == "json":
                console.print(JSON(result.model_dump_json()))
            else:
                _display_result_table(result, verbose)

        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
            if verbose:
                import traceback

                console.print(traceback.format_exc())

    asyncio.run(_classify())


@app.command()
def categories():
    """List all available categories."""

    async def _list_categories():
        try:
            settings = Settings()
            classifier = ComplaintClassifier(settings)

            categories = classifier.get_available_categories()

            table = Table(title="Available Categories")
            table.add_column("Main Category", style="cyan", no_wrap=True)
            table.add_column("Description", style="magenta")
            table.add_column("Sub-categories", style="green")

            for main_cat, data in categories.items():
                sub_cats = list(data["sub_categories"].keys())
                sub_cat_text = "\n".join(sub_cats[:5])  # Show first 5
                if len(sub_cats) > 5:
                    sub_cat_text += f"\n... and {len(sub_cats) - 5} more"

                table.add_row(
                    main_cat,
                    data["description"][:100] + "..."
                    if len(data["description"]) > 100
                    else data["description"],
                    sub_cat_text,
                )

            console.print(table)

        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")

    asyncio.run(_list_categories())


@app.command()
def validate():
    """Validate system setup and configuration."""

    async def _validate():
        try:
            settings = Settings()
            classifier = ComplaintClassifier(settings)

            console.print("[blue]Validating system setup...[/blue]")

            validation = await classifier.validate_setup()
            stats = classifier.get_statistics()

            # Display validation results
            table = Table(title="System Validation")
            table.add_column("Component", style="cyan")
            table.add_column("Status", style="bold")
            table.add_column("Details", style="dim")

            for component, status in validation.items():
                status_text = "✓ PASS" if status else "✗ FAIL"
                status_color = "green" if status else "red"

                if component == "knowledge_base":
                    details = f"{stats['knowledge_base']['main_categories']} main categories, {stats['knowledge_base']['total_sub_categories']} sub-categories"
                elif component == "configuration":
                    details = "API keys configured" if status else "Missing API keys"
                else:
                    details = (
                        "All agents initialized"
                        if status
                        else "Agent initialization failed"
                    )

                table.add_row(
                    component.title(), status_text, details, style=status_color
                )

            console.print(table)

            # Display configuration
            config_panel = Panel(
                f"Environment: {settings.environment}\n"
                f"Fast Track: {'Enabled' if settings.workflow.enable_fast_track else 'Disabled'}\n"
                f"Triage Threshold: {settings.workflow.triage_confidence_threshold}\n"
                f"Fallback Category: {settings.workflow.fallback_category}",
                title="Configuration",
                border_style="blue",
            )
            console.print(config_panel)

        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")

    asyncio.run(_validate())


def _display_result_table(result, verbose: bool = False):
    """Display classification result in table format."""

    # Main result
    table = Table(title="Classification Result")
    table.add_column("Property", style="cyan")
    table.add_column("Value", style="bold")

    table.add_row("Category", result.category)
    table.add_row(
        "Confidence", f"{result.confidence:.2f}" if result.confidence else "N/A"
    )
    table.add_row("Path Taken", result.path_taken.title())
    table.add_row(
        "Review Passed",
        str(result.review_passed) if result.review_passed is not None else "N/A",
    )

    console.print(table)

    # Reasoning chain (if verbose)
    if verbose and result.reasoning_chain:
        console.print("\n[blue]Reasoning Chain:[/blue]")
        for i, step in enumerate(result.reasoning_chain, 1):
            agent = step.get("agent", "Unknown")
            metadata = step.get("metadata", {})
            step_type = metadata.get("step", "unknown")

            panel_title = f"Step {i}: {agent} ({step_type})"

            # Format step content
            content = []
            if "input" in step:
                content.append(f"[dim]Input:[/dim] {_format_step_data(step['input'])}")
            if "output" in step:
                content.append(
                    f"[dim]Output:[/dim] {_format_step_data(step['output'])}"
                )

            panel = Panel("\n".join(content), title=panel_title, border_style="dim")
            console.print(panel)


def _format_step_data(data, max_length: int = 200) -> str:
    """Format step data for display."""
    if isinstance(data, dict):
        # Show key fields only
        if "content" in data:
            content = data["content"][:max_length]
            if len(data["content"]) > max_length:
                content += "..."
            return content
        elif "category" in data:
            return f"Category: {data['category']}"
        elif "candidates" in data:
            candidates = data["candidates"]
            if candidates:
                return f"Top candidate: {candidates[0].get('category', 'N/A')}"
        else:
            return str(data)[:max_length]
    else:
        return str(data)[:max_length]


if __name__ == "__main__":
    load_dotenv()
    app()
