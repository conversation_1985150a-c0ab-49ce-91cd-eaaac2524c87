["tests/test_classifier.py::TestComplaintClassifier::test_classifier_initialization", "tests/test_knowledge.py::TestContextInjector::test_create_main_category_context", "tests/test_knowledge.py::TestContextInjector::test_create_review_context", "tests/test_knowledge.py::TestContextInjector::test_create_sub_category_context", "tests/test_knowledge.py::TestContextInjector::test_format_main_category_prompt_section", "tests/test_knowledge.py::TestContextInjector::test_format_review_prompt_section", "tests/test_knowledge.py::TestContextInjector::test_format_sub_category_prompt_section", "tests/test_knowledge.py::TestKnowledgeManager::test_get_main_category_definitions", "tests/test_knowledge.py::TestKnowledgeManager::test_get_statistics", "tests/test_knowledge.py::TestKnowledgeManager::test_get_sub_category_definitions", "tests/test_knowledge.py::TestKnowledgeManager::test_get_sub_category_definitions_invalid_main", "tests/test_knowledge.py::TestKnowledgeManager::test_load_invalid_json", "tests/test_knowledge.py::TestKnowledgeManager::test_load_knowledge_base", "tests/test_knowledge.py::TestKnowledgeManager::test_load_nonexistent_file", "tests/test_knowledge.py::TestKnowledgeManager::test_validate_classification", "tests/test_knowledge.py::TestKnowledgeModels::test_category_definition", "tests/test_knowledge.py::TestKnowledgeModels::test_knowledge_base_methods", "tests/test_knowledge.py::TestKnowledgeModels::test_sub_category_definition"]