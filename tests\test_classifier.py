"""
Tests for the complaint classifier system.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch

from src.complaint_classifier import ComplaintClassifier
from src.complaint_classifier.models import ComplaintCase, ClassificationResult
from src.complaint_classifier.config import Settings


class TestComplaintClassifier:
    """Test cases for the ComplaintClassifier."""
    
    @pytest.fixture
    def mock_settings(self):
        """Create mock settings for testing."""
        settings = Settings()
        settings.openai_api_key = "test-key"
        settings.knowledge_base_path = "src/data/categories_def.json"
        return settings
    
    @pytest.fixture
    def classifier(self, mock_settings):
        """Create a classifier instance for testing."""
        return ComplaintClassifier(mock_settings)
    
    def test_classifier_initialization(self, classifier):
        """Test that classifier initializes correctly."""
        assert classifier is not None
        assert classifier.workflow_engine is not None
        assert classifier.settings is not None
    
    def test_get_available_categories(self, classifier):
        """Test getting available categories."""
        categories = classifier.get_available_categories()
        
        assert isinstance(categories, dict)
        assert len(categories) > 0
        
        # Check structure
        for main_cat, data in categories.items():
            assert "description" in data
            assert "sub_categories" in data
            assert isinstance(data["sub_categories"], dict)
    
    def test_get_statistics(self, classifier):
        """Test getting system statistics."""
        stats = classifier.get_statistics()
        
        assert isinstance(stats, dict)
        assert "knowledge_base" in stats
        assert "configuration" in stats
        
        # Check knowledge base stats
        kb_stats = stats["knowledge_base"]
        assert "main_categories" in kb_stats
        assert "total_sub_categories" in kb_stats
        assert kb_stats["main_categories"] > 0
    
    @pytest.mark.asyncio
    async def test_validate_setup(self, classifier):
        """Test system validation."""
        validation = await classifier.validate_setup()
        
        assert isinstance(validation, dict)
        assert "knowledge_base" in validation
        assert "agents" in validation
        assert "configuration" in validation
        
        # Knowledge base should be valid
        assert validation["knowledge_base"] is True
    
    @pytest.mark.asyncio
    async def test_classify_simple_case(self, classifier):
        """Test classification of a simple case."""
        # Mock the workflow engine to avoid actual API calls
        with patch.object(classifier.workflow_engine, 'classify_complaint') as mock_classify:
            mock_result = ClassificationResult(
                category="路燈故障",
                confidence=0.95,
                path_taken="fast_track",
                reasoning_chain=[],
                review_passed=None
            )
            mock_classify.return_value = mock_result
            
            result = await classifier.classify(
                content="路燈不亮了，請派人修理",
                case_id="TEST001"
            )
            
            assert isinstance(result, ClassificationResult)
            assert result.category == "路燈故障"
            assert result.confidence == 0.95
            assert result.path_taken == "fast_track"


class TestComplaintCase:
    """Test cases for ComplaintCase model."""
    
    def test_complaint_case_creation(self):
        """Test creating a complaint case."""
        case = ComplaintCase(
            content="測試陳情內容",
            case_id="TEST001",
            metadata={"source": "web"}
        )
        
        assert case.content == "測試陳情內容"
        assert case.case_id == "TEST001"
        assert case.metadata["source"] == "web"
    
    def test_complaint_case_minimal(self):
        """Test creating a minimal complaint case."""
        case = ComplaintCase(content="測試內容")
        
        assert case.content == "測試內容"
        assert case.case_id is None
        assert case.metadata is None


class TestClassificationResult:
    """Test cases for ClassificationResult model."""
    
    def test_classification_result_creation(self):
        """Test creating a classification result."""
        result = ClassificationResult(
            category="道路維護",
            confidence=0.85,
            path_taken="expert_review",
            reasoning_chain=[{"step": 1, "agent": "test"}],
            review_passed=True
        )
        
        assert result.category == "道路維護"
        assert result.confidence == 0.85
        assert result.path_taken == "expert_review"
        assert len(result.reasoning_chain) == 1
        assert result.review_passed is True
    
    def test_classification_result_minimal(self):
        """Test creating a minimal classification result."""
        result = ClassificationResult(category="其他")
        
        assert result.category == "其他"
        assert result.confidence is None
        assert result.path_taken == "expert_review"  # default
        assert result.reasoning_chain == []
        assert result.review_passed is None


@pytest.mark.integration
class TestIntegration:
    """Integration tests for the complete system."""
    
    @pytest.fixture
    def classifier(self):
        """Create a classifier for integration testing."""
        settings = Settings()
        # Use environment variables for API keys in integration tests
        return ComplaintClassifier(settings)
    
    @pytest.mark.asyncio
    async def test_end_to_end_classification(self, classifier):
        """Test end-to-end classification (requires API keys)."""
        # Skip if no API keys available
        validation = await classifier.validate_setup()
        if not validation.get("configuration", False):
            pytest.skip("No API keys configured for integration test")
        
        # Test with a simple case
        result = await classifier.classify(
            content="路燈不亮，需要維修",
            case_id="INTEGRATION_TEST_001"
        )
        
        assert isinstance(result, ClassificationResult)
        assert result.category is not None
        assert result.path_taken in ["fast_track", "expert_review"]
    
    @pytest.mark.asyncio
    async def test_complex_case_classification(self, classifier):
        """Test classification of a complex case."""
        # Skip if no API keys available
        validation = await classifier.validate_setup()
        if not validation.get("configuration", False):
            pytest.skip("No API keys configured for integration test")
        
        # Test with a complex case
        complex_content = """
        我住在市中心，最近發現我們社區附近有很多問題：
        1. 路燈經常不亮，晚上很危險
        2. 路面有很多坑洞，騎車很危險
        3. 垃圾車來的時間不固定
        4. 附近有工地噪音很大
        請政府幫忙解決這些問題。
        """
        
        result = await classifier.classify(
            content=complex_content,
            case_id="INTEGRATION_TEST_002"
        )
        
        assert isinstance(result, ClassificationResult)
        assert result.category is not None
        # Complex cases should go through expert review
        assert result.path_taken == "expert_review"
        assert result.review_passed is not None
